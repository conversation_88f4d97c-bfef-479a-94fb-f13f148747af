#include "functions.h"
#include "hangxe.h"
#include <iostream>
#include <algorithm>
using namespace std;

void nhapslx(int &nx)
{
    cin >> nx;
    if (nx <= 0)
        cout << "Nhap so luong xe > 0";
}

void nhapslh(int &nh)
{
    cin >> nh;
}

void nhapttx(int nx, oto a[])
{
    for (int i = 0; i < nx; i++)
    {
        a[i].nhap();
    }
}

void nhaptth(int nh, hangxe b[])
{
    for (int i = 0; i < nh; i++)
    {
        b[i].nhap();
    }
}
void xuly(int nx, oto a[], hangxe b[])
{
    string hang1;
    getline(cin, hang1);
    for (int i = 0; i < nx; i++)
    {
        if (a[i].gethang() == hang1)
        {
            a[i].in(b);
        }
    }
}

void outxe(int nx, oto a[], hangxe b[])
{
    for (int i = 0; i < nx; i++)
        a[i].in(b);
}

void outhang(int nh, hangxe b[])
{
    for (int i = 0; i < nh; i++)
    {
        b[i].in();
    }
}

void sapxepGLB(int nx, oto a[], hangxe b[])
{
    sort(a, a + nx, [](oto &x, oto &y)
         { return x.lanbanh() < y.lanbanh(); });

    for (int i = 0; i < nx; i++)
    {
        a[i].in(b);
        cout << "  Gia lan banh: " << a[i].lanbanh() << endl;
    }
}

void tongdonhang(oto a[], int nx)
{
    int tong = 0;
    for (int i = 0; i < nx; i++)
    {
        tong += a[i].getgny();
    }
    cout << endl
         << tong;
}

void thongke(oto a[], hangxe b[], int nx, int nh)
{
    int dem = 0;
    for (int i = 0; i < nh; i++)
    {
        for (int j = 0; j < nx; j++)
            if (b[i].gettenhang() == a[j].gethang())
            {
                dem++;
            }
        b[i].in();
        cout << dem;
        dem = 0;
    }
}

void inmahang(hangxe b[], oto a[], int nx, int nh)
{
    string timmahang, hangxe;
    getline(cin, timmahang);
    for (int i = 0; i < nh; i++)
    {
        if (timmahang == b[i].getmahangxe())
        {
            hangxe = b[i].gettenhang();
        }
    }
    for (int i = 0; i < nx; i++)
    {
        if (a[i].gethang() == hangxe)
        {
            a[i].in(b);
        }
    }
}