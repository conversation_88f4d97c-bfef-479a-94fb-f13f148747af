#ifndef FUNCTIONS_H
#define FUNCTIONS_H

#include "oto.h"
#include "hangxe.h"
using namespace std;

void nhapslx(int &nx);
void nhapslh(int &nh);
void nhapttx(int nx, oto a[]);
void nhaptth(int &nh, hangxe b[]);
void xuly(int nx, oto a[], hangxe b[]);
void outxe(int n, oto a[], hangxe b[]);
void outhang(int nh, hangxe b[]);
void sapxepGLB(int nx, oto a[]);
void tongdonhang(oto a[], int nx);
void thongke(oto a[], hangxe b[], int nx, int nh);
void inmahang(hangxe b[], oto a[], int nx, int nh);
#endif
