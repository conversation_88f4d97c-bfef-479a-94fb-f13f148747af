#include "hangxe.h"
#include <iostream>
using namespace std;

void hangxe::nhap()
{
    getline(cin, mahangxe);
    getline(cin, tenhang);
    getline(cin, nuoc);
    cin >> ntl;
    cin.ignore();
}

void hangxe::in()
{
    cout << mahangxe << "-" << tenhang << ": " << endl;
}

void hangxe::setmahangxe(string d) { mahangxe = d; }
string hangxe::getmahangxe() { return mahangxe; }

void hangxe::settenhang(string d) { tenhang = d; }
string hangxe::gettenhang() { return tenhang; }

void hangxe::setnuoc(string d) { nuoc = d; }
string hangxe::getnuoc() { return nuoc; }

void hangxe::setnamthanhlap(int d) { ntl = d; }
int hangxe::getnamthanhlap() { return ntl; }