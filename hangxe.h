#ifndef HANGXE_H
#define HANGXE_H
#include <string>
using namespace std;
class hangxe
{
private:
    string mahangxe, tenhang, nuoc;
    int ntl;

public:
    void nhap();
    void in();

    void setmahangxe(string d);
    string getmahangxe();

    void settenhang(string d);
    string gettenhang();

    void setnuoc(string d);
    string getnuoc();

    void setnamthanhlap(int d);
    int getnamthanhlap();
};
#endif