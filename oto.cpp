#include "oto.h"
#include "hangxe.h"
#include <iostream>
using namespace std;
void oto::nhap()
{
    getline(cin, maxe);
    getline(cin, ten);
    cin >> nsx;
    // cin.ignore();
    // getline(cin, hang);
    cin >> index;
    cin >> gny;
    cin.ignore();
    getline(cin, mau);
}

void oto::in(hangxe b[])
{
    cout << maxe << "-" << ten << "-" << nsx
         << "-" << b[index].gettenhang() << "-" << gny << "-" << mau << endl;
}

void oto::setmaxe(string d) { maxe = d; }
string oto::getmaxe() { return maxe; }

void oto::setten(string d) { ten = d; }
string oto::getten() { return ten; }

void oto::setnsx(int d)
{
    if (d > 2025)
        cout << "nam khong hop le";
    else
        nsx = d;
}
int oto::getnsx() { return nsx; }

void oto::sethang(string d) { hang = d; }
string oto::gethang() { return hang; }

void oto::setgny(long long d) { gny = d; }
long long oto::getgny() { return gny; }

void oto::setmau(string d) { mau = d; }
string oto::getmau() { return mau; }

double oto::phidk()
{
    return 0.072 * gny + 500000 + 340000 + 480000 + 2160000;
}

double oto::lanbanh()
{
    return gny + phidk();
}
