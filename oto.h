#ifndef OTO_H
#define OTO_H

#include <string>
#include "hangxe.h"
using namespace std;

class oto
{
private:
    string maxe, ten, hang, mau;
    long long nsx, gny, index;

public:
    void nhap();
    void in(hangxe b[]);

    void setmaxe(string d);
    string getmaxe();

    void setten(string d);
    string getten();

    void setnsx(int d);
    int getnsx();

    void sethang(string d);
    string gethang();

    void setgny(long long d);
    long long getgny();

    void setmau(string d);
    string getmau();

    double phidk();
    double lanbanh();
};

#endif
